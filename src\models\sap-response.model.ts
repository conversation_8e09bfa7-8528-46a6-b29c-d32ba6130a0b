import {Entity, model, property} from '@loopback/repository';

@model()
export class SapResponse extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'string',
  })
  Location?: string;
  @property({
    type: 'string',
  })
  Title?: string;

  @property({
    type: 'number',
  })
  dataType?: number;

  @property({
    type: 'number',
  })
  categoryType?: number;

  @property({
    type: 'string',
  })
  WasteCategory?: string;

  @property({
    type: 'string',
  })
  Date?: string;

  @property({
    type: 'string',
  })
  Type?: string;

  @property({
    type: 'string',
  })
  sapId?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  FuelType?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  EmpId?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTOrigin?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  Month?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  PurchaseOrder?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  HSNCode?: string | null;
  @property({
    type: 'any'
  })
  TotalSpent?: any;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  efKey?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  MaterialCategory?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  MaterialDescription?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  VendorCode?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  SupplierName?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  Plant?: string | null;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTDestination?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  BTMode?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  ModeOfTransportation?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  WasteDescription?: string | null;

  @property({
    type: 'any'
  })
  Quantity?: any;

  @property({
    type: 'any'
  })
  ProductType?: any;
  @property({
    type: 'any'
  })
  MaterialNumber?: any;
  @property({
    type: 'any'
  })
  OriginLocation?: any;
  @property({
    type: 'any'
  })
  ContractType?: any;
  @property({
    type: 'any'
  })
  EmpType?: any;
  @property({
    type: 'any'
  })
  DOB?: any;
  @property({
    type: 'any'
  })
  Gender?: any;
  @property({
    type: 'any'
  })
  OfficeCity?: any;
  @property({
    type: 'any'
  })
  OfficeLocation?: any;
  @property({
    type: 'any'
  })
  InvoiceAmount?: any;
  @property({
    type: 'any'
  })
  DestinationLocation?: any;

  @property({
    type: 'any'
  })
  Currency?: any;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  UoM?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  fetched_on?: string | null;

  @property({
    type: 'number',
  })
  level?: number; // Stores the mapped value of Qty

  @property({
    type: 'number',
  })
  locationId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  // Employee Analytics Properties
  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  Value?: any; // Count value for analytics

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  dpId?: string | null; // DPTVS#### codes

  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeType?: any; // Employee Type (White Collar, Blue Collar, etc.)

  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeGender?: any; // Male/Female

  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeGrade?: any; // B3, D1, etc.

  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeAge?: any; // Age or age group

  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeDOE?: any; // Date of Exit

  @property({
    type: 'any',
    jsonSchema: {
      nullable: true,
    }
  })
  EmployeeDOJ?: any; // Date of Joining

  constructor(data?: Partial<SapResponse>) {
    super(data);
  }
}

export interface SapResponseRelations {
  // describe navigational properties here
}

export type SapResponseWithRelations = SapResponse & SapResponseRelations;
