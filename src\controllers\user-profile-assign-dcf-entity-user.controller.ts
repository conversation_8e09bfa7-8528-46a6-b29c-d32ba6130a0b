import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {
  AssignDcfEntityUser,
  UserProfile,
} from '../models';
import {FormCollectionRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, UserProfileRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class UserProfileAssignDcfEntityUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @get('/user-profiles/{id}/assign-dcf-entity-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDcfEntityUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfEntityUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfEntityUser>,
  ): Promise<AssignDcfEntityUser[]> {
    return this.userProfileRepository.assignDcfEntityUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-dcf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfEntityUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntityUser, {
            title: 'NewAssignDcfEntityUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDcfEntityUser: Omit<AssignDcfEntityUser, 'id'>,
  ): Promise<any> {
    const {tier0_id, tier1_id, tier2_id, tier3_id, dcfId, entityAssId, start_date} = assignDcfEntityUser
    if (id !== 94) {
      const frequency_list = [{name: 'Monthly', id: 1}, {name: 'Bi-Monthly', id: 2}, {name: 'Quarterly', id: 3}, {name: 'Annually', id: 4}, {name: 'Bi-Annually', id: 5}]

      const find = await this.userProfileRepository.assignDcfEntityUsers(id).find({where: {entityAssId, tier0_id, tier1_id, tier2_id, tier3_id, dcfId}})
      console.log(tier0_id, tier1_id, tier2_id, tier3_id, dcfId, entityAssId, tier0_id, find)
      if (find.length) {
        let last = find[find.length - 1]
        if (last.end_date && start_date) {
          const lastMonth = DateTime.fromISO(last?.end_date, {zone: 'utc'}).plus({months: 1})

          const expectedMonth = DateTime.fromISO(start_date || '', {zone: 'utc'});
          console.log(lastMonth.toFormat('MMM-yyyy'), expectedMonth.toFormat('MMM-yyyy'))
          if (lastMonth.toFormat('MMM-yyyy') === expectedMonth.toFormat('MMM-yyyy')) {

            const newData = await this.userProfileRepository.assignDcfEntityUsers(id).create(assignDcfEntityUser)
            if (assignDcfEntityUser?.reporter_ids && id && id !== 94) {
              const adminDetail = (await this.userProfileController.filteredUP({where: {id}}))?.[0]
              let newReporterIds = assignDcfEntityUser?.reporter_ids || []
              let newReviewerIds = assignDcfEntityUser?.reviewer_ids || []
              const form = await this.formCollectionRepository.findById(assignDcfEntityUser.dcfId)
              let entity: any = 'NA'
              if (assignDcfEntityUser.level === 0) {
                entity = 'Corporate'
              } else if (assignDcfEntityUser.level === 1) {
                entity = (await this.locationOneRepository.findById(assignDcfEntityUser.tier1_id)).name
              } else if (assignDcfEntityUser.level === 2) {
                entity = (await this.locationTwoRepository.findById(assignDcfEntityUser.tier2_id)).name
              } else if (assignDcfEntityUser.level === 3) {
                entity = (await this.locationThreeRepository.findById(assignDcfEntityUser.tier3_id)).name

              }
              let userData: any = []

              const allusers = Array.from(new Set([...newReporterIds, ...newReviewerIds]))
              if (allusers.length) {
                userData = await this.userProfileController.getUserProfileList({where: {id: {inq: allusers}}})
              }
              const reporterList = newReporterIds.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
              const reviewerList = newReviewerIds.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))



              for (const reporter of reporterList) {
                if (reporter.to && reporter.name) {
                  const body = `<p>Dear ${reporter.name}</p> <p style='margin: 5px 0px;'>You have been assigned as the <strong>Data Reporter</strong> for <strong> "${form.title}"</strong> for the reporting entity <strong>${entity}</strong>. Please find the assignment details below:</p> <ul>  <li style='margin: 5px 0px;'><strong>Data Frequency:</strong> ${frequency_list?.find(x => x.id === assignDcfEntityUser?.frequency)?.name || ''}</li> <li style='margin: 5px 0px;'><strong>Reporting Period Start:</strong> ${DateTime.fromISO(start_date || '', {zone: 'utc'}).plus({days: 1}).toFormat('LLL-yyyy')}</li> <li style='margin: 5px 0px;'><strong>Assigned Reviewer:</strong>${newReviewerIds.length ? reviewerList.map(i => i.name).join(',') : 'Self Review'}</li> <li style='margin: 5px 0px;'><strong>Submission Due Date:</strong> 20th of the month succeeding the reporting period</li></ul> <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform</a> to log.</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `
                  // const info = await this.sqsService.sendEmail(reporter.to, 'Assignment of Sustainability Data Collection Form – NAVIGOS', body, []).then((info) => {
                  //   console.log(info)
                  //   return info
                  // }).catch((err) => {
                  //   console.log(err)
                  //   return err
                  // })
                }

              }




              for (const reviewer of reviewerList) {
                if (reviewer.to && reviewer.name) {
                  const body = `<p>Dear ${reviewer.name}</p> <p style='margin: 5px 0px;'>You have been assigned as the <strong>Data Reviewer</strong> for <strong>"${form.title}"</strong> for the reporting entity <strong>${entity}</strong>. Please find the assignment details below:</p> <ul> <li style='margin: 5px 0px;'><strong>Data Frequency:</strong> ${frequency_list?.find(x => x.id === assignDcfEntityUser?.frequency)?.name || ''}</li> <li style='margin: 5px 0px;'><strong>Reporting Period Start:</strong> ${DateTime.fromISO(start_date || '', {zone: 'utc'}).plus({days: 1}).toFormat('LLL-yyyy')}</li> <li style='margin: 5px 0px;'><strong>Assigned Reporter:</strong>${newReporterIds.length ? reporterList.map(i => i.name).join(',') : ''}</li> <li style='margin: 5px 0px;'><strong>Submission Due Date:</strong> 25th of the month succeeding the reporting period </li></ul> <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform </a> to log in and complete the data review by the due date.</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `
                  // const info = await this.sqsService.sendEmail(reviewer.to, 'Assignment of Sustainability Data Collection Form for Review  – NAVIGOS', body, []).then((info) => {
                  //   console.log(info)
                  //   return info
                  // }).catch((err) => {
                  //   console.log(err)
                  //   return err
                  // })
                }

              }



            }
            return {status: true, data: {...newData, end_date: null}}
          } else {
            return {status: false, data: find}
          }
        } else {
          return {status: false, data: find}
        }


      } else {
        const newData = await this.userProfileRepository.assignDcfEntityUsers(id).create(assignDcfEntityUser)
        if (assignDcfEntityUser?.reporter_ids && id && id !== 94) {
          const adminDetail = (await this.userProfileController.filteredUP({where: {id}}))?.[0]

          let newReporterIds = assignDcfEntityUser?.reporter_ids || []
          let newReviewerIds = assignDcfEntityUser?.reviewer_ids || []
          const form = await this.formCollectionRepository.findById(assignDcfEntityUser.dcfId)
          let entity: any = 'NA'
          if (assignDcfEntityUser.level === 0) {
            entity = 'Corporate'
          } else if (assignDcfEntityUser.level === 1) {
            entity = (await this.locationOneRepository.findById(assignDcfEntityUser.tier1_id)).name
          } else if (assignDcfEntityUser.level === 2) {
            entity = (await this.locationTwoRepository.findById(assignDcfEntityUser.tier2_id)).name
          } else if (assignDcfEntityUser.level === 3) {
            entity = (await this.locationThreeRepository.findById(assignDcfEntityUser.tier3_id)).name

          }
          let userData: any = []

          const allusers = Array.from(new Set([...newReporterIds, ...newReviewerIds]))
          if (allusers.length) {
            userData = await this.userProfileController.getUserProfileList({where: {id: {inq: allusers}}})
          }
          const reporterList = newReporterIds.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
          const reviewerList = newReviewerIds.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))

          console.log(reporterList, reviewerList, allusers)

          for (const reporter of reporterList) {
            if (reporter.to && reporter.name) {
              const body = `<p>Dear ${reporter.name}</p> <p style='margin: 5px 0px;'>You have been assigned as the <strong>Data Reporter</strong> for <strong> "${form.title}"</strong> for the reporting entity <strong>${entity}</strong>. Please find the assignment details below:</p> <ul> <li style='margin: 5px 0px;'><strong>Data Frequency:</strong> ${frequency_list?.find(x => x.id === assignDcfEntityUser?.frequency)?.name || ''}</li> <li style='margin: 5px 0px;'><strong>Reporting Period Start:</strong> ${DateTime.fromISO(start_date || '', {zone: 'utc'}).plus({days: 1}).toFormat('LLL-yyyy')}</li> <li style='margin: 5px 0px;'><strong>Assigned Reviewer:</strong>${newReviewerIds.length ? reviewerList.map(i => i.name).join(',') : 'Self Review'}</li> <li style='margin: 5px 0px;'><strong>Submission Due Date:</strong> 20th of the month succeeding the reporting period</li></ul> <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform</a> to log.</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `

              // const info = await this.sqsService.sendEmail(reporter.to, 'Assignment of Sustainability Data Collection Form – NAVIGOS', body, []).then((info) => {
              //   console.log(info)
              //   return info
              // }).catch((err) => {
              //   console.log(err)
              //   return err
              // })
            }

          }




          for (const reviewer of reviewerList) {
            if (reviewer.to && reviewer.name) {
              const body = `<p>Dear ${reviewer.name}</p> <p style='margin: 5px 0px;'>You have been assigned as the <strong>Data Reviewer</strong> for <strong>"${form.title}"</strong> for the reporting entity <strong>${entity}</strong>. Please find the assignment details below:</p> <ul> <li style='margin: 5px 0px;'><strong>Data Frequency:</strong> ${frequency_list?.find(x => x.id === assignDcfEntityUser?.frequency)?.name || ''}</li> <li style='margin: 5px 0px;'><strong>Reporting Period Start:</strong> ${DateTime.fromISO(start_date || '', {zone: 'utc'}).plus({days: 1}).toFormat('LLL-yyyy')}</li> <li style='margin: 5px 0px;'><strong>Assigned Reporter:</strong>${newReporterIds.length ? reporterList.map(i => i.name).join(',') : ''}</li> <li style='margin: 5px 0px;'><strong>Submission Due Date:</strong> 25th of the month succeeding the reporting period </li> </ul> <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform </a> to log in and complete the data review by the due date.</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `

              // const info = await this.sqsService.sendEmail(reviewer.to, 'Assignment of Sustainability Data Collection Form for Review  – NAVIGOS', body, []).then((info) => {
              //   console.log(info)
              //   return info
              // }).catch((err) => {
              //   console.log(err)
              //   return err
              // })
            }

          }



        }
        return {status: true, data: {...newData, end_date: null}}
      }
    } else {
      return this.userProfileRepository.assignDcfEntityUsers(id).create(assignDcfEntityUser)
    }


  }

  @patch('/user-profiles/{id}/assign-dcf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDcfEntityUser PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: Partial<AssignDcfEntityUser>,
    @param.query.object('where', getWhereSchemaFor(AssignDcfEntityUser)) where?: Where<AssignDcfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDcfEntityUsers(id).patch(assignDcfEntityUser, where);
  }

  @del('/user-profiles/{id}/assign-dcf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDcfEntityUser DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignDcfEntityUser)) where?: Where<AssignDcfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDcfEntityUsers(id).delete(where);
  }
}
