import {inject} from '@loopback/core';
import * as AWS from 'aws-sdk';
import axios from 'axios';
import {DateTime} from 'luxon';
import {ParquetReader} from 'parquetjs-lite';
import purchase from '../data/purchase.json';
import {<PERSON>p<PERSON><PERSON>, <PERSON>p<PERSON><PERSON><PERSON>us, SapNonHazardous, SapResponse} from '../models';
import {NewEfSubcategory1Repository, SapFuelRepository, SapHazardousRepository, SapNonHazardousRepository, SapResponseRepository} from '../repositories';
AWS.config.update({
  accessKeyId: process.env.AWS_TVS_CLIENT_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_TVS_CLIENT_SECRET_ACCESS_KEY,
  region: process.env.AWS_TVS_CLIENT_REGION,
});

const s3 = new AWS.S3();
// const FY25Months = ['Apr-2024', 'May-2024', 'Jun-2024', 'Jul-2024', 'Aug-2024', 'Sep-2024', 'Oct-2024', 'Nov-2024', 'Dec-2024', 'Jan-2025', 'Feb-2025', 'Mar-2025'];
const FY25Months = ['Apr-2025', 'May-2025'];

export class SapService {
  constructor(
    @inject('repositories.SapResponseRepository') private sapResponseRepository: SapResponseRepository,
    @inject('repositories.SapFuelRepository') private s3Repository: SapFuelRepository,
    @inject('repositories.SapHazardousRepository') private s3HazardRepository: SapHazardousRepository,
    @inject('repositories.SapNonHazardousRepository') private s3NonHazardRepository: SapNonHazardousRepository,
    @inject('repositories.NewEfSubcategory1Repository') private newEfSubcategory1Repository: NewEfSubcategory1Repository,

  ) { }

  async listData(bucketName: string, folderName: string): Promise<any> {

    const params = {
      Bucket: bucketName,
      Prefix: folderName,
    };

    try {
      const data = await s3.listObjectsV2(params).promise();

      if (data.Contents && data.Contents.length > 0) {

        const fileKeys = data.Contents

        return fileKeys;
        // return data.Contents
      } else {
        console.log('No files found in the folder');
        return [];
      }
    } catch (error) {
      console.error('Error listing files from S3:', error);
      throw new Error('Could not list files from S3');
    }

  }
  async storeFolder(bucketName: string, folderName: string, batchSize: number = 5000, type: number, keep: boolean = false): Promise<void> {
    const allRecords: any[] = [];

    try {

      const params = {
        Bucket: bucketName,
        Prefix: folderName,
      };

      const listedObjects = await s3.listObjectsV2(params).promise();
      const parquetFiles = listedObjects.Contents?.filter(file => file.Key?.endsWith('.parquet'));

      if (!parquetFiles || parquetFiles.length === 0) {
        console.log(`No .parquet files found in folder ${folderName}.`);
        return;
      }
      const totalFiles = parquetFiles;
      console.log(totalFiles)
      for (const file of totalFiles) {
        if (file.Key) {

          console.log(`Processing file: ${file.Key}`, DateTime.utc().setZone('Asia/Kolkata').toFormat('yyyy-MM-dd HH:mm:ss'));
          const buffer = await this.fetchParquetFromS3(bucketName, file.Key);
          if (type === 1) {
            const fuelRecord = await this.FuelJSON(buffer);
            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP1'})

            }
            await this.pushData(fuelRecord, batchSize);

          } else if (type === 2) {
            const hazardRecord = await this.HazardousJSON(buffer);
            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP2'})

            }
            await this.pushData(hazardRecord, batchSize);
          } else if (type === 3) {
            const nonHazardRecord = await this.NonHazardousJSON(buffer);
            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP3'})

            }
            await this.pushData(nonHazardRecord, batchSize);
          } else if (type === 4) {

            const purchaseRecord = await axios.post('https://4xqp419ofi.execute-api.ap-south-1.amazonaws.com/default/ExcelProcessingTVS', {key: file.Key, type: 1})


            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP4'})

            }

            await this.pushUpdatedOnly(purchaseRecord.data.filter((x: any) => (x.TotalSpent != null) && x?.MaterialCategory && x?.Month && FY25Months.includes(x?.Month)).map((x: any) => ({TotalSpent: x.TotalSpent, tier1_id: 103, level: 1, locationId: 103, Plant: 'India', Location: 'India', Month: x.Month, Date: DateTime.fromFormat(x.Month, 'LLL-yyyy').toFormat('yyyyMMdd'), MaterialCategory: x.MaterialCategory, sapId: "SAP4", dataType: 4, UoM: 'USD', userProfileId: 289, Title: 'Purchase Goods & Services', fetched_on: DateTime.utc().toString(), })), batchSize, 'SAP4');

          } else if (type === 5) {
            const purchaseRecord = await axios.post('https://4xqp419ofi.execute-api.ap-south-1.amazonaws.com/default/ExcelProcessingTVS', {key: file.Key, type: 1})

            // const purchaseRecord = await this.PurchaseGoodsJSON(buffer);
            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP5'})

            }

            await this.pushUpdatedOnly(purchaseRecord.data.filter((x: any) => (x.TotalSpent != null) && x?.MaterialCategory && x?.Month && FY25Months.includes(x?.Month)).map((x: any) => ({TotalSpent: x.TotalSpent, tier1_id: 103, level: 1, locationId: 103, Plant: 'India', Location: 'India', Month: x.Month, Date: DateTime.fromFormat(x.Month, 'LLL-yyyy').toFormat('yyyyMMdd'), MaterialCategory: x.MaterialCategory, sapId: "SAP5", dataType: 5, UoM: 'USD', userProfileId: 289, Title: 'Capital Goods', fetched_on: DateTime.utc().toString(), })), batchSize, 'SAP5');

            // const capitalRecord = await this.CapitalGoodsJSON(buffer);
            // if (keep) {
            //   // await this.sapResponseRepository.deleteAll({sapId: 'SAP5'})

            // }
            // await this.pushData(capitalRecord, batchSize);
          } else if (type === 6) {
            const btRecord = await this.BusinessTravelJSON(buffer);
            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP6'})

            }
            await this.pushData(btRecord, batchSize);
          } else if (type === 7) {
            const purchaseRecord = await axios.post('https://4xqp419ofi.execute-api.ap-south-1.amazonaws.com/default/ExcelProcessingTVS', {key: file.Key, type: 2})

            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP7'})

            }
            console.log(purchaseRecord.data?.length, purchaseRecord.data?.[0])
            await this.pushUpdatedOnlyDownStream(purchaseRecord.data.filter((x: any) => (x.Total != null) && x?.Month && x?.Mode && FY25Months.includes(x?.Month)).map((x: any) => ({InvoiceAmount: x.Total, tier1_id: 103, level: 1, locationId: 103, Plant: 'India', Location: 'India', Month: x.Month, Date: DateTime.fromFormat(x.Month, 'LLL-yyyy').toFormat('yyyyMMdd'), ModeOfTransportation: x.Mode, sapId: "SAP7", dataType: 7, UoM: 'USD', userProfileId: 289, Title: 'DownStream Transportation', fetched_on: DateTime.utc().toString(), })), batchSize, 'SAP7');

          } else if (type === 8) {
            const hrEmployeeRecord = await this.HREmployeeJSON(buffer);
            if (keep) {
              // await this.sapResponseRepository.deleteAll({sapId: 'SAP6'})

            }
            await this.pushData(hrEmployeeRecord, batchSize);
          }
          else {
            console.error(`Invalid type provided: ${type}`);
          }


        }
      }

    } catch (error) {
      console.error('Error listing or processing files from folder:', error);
      throw new Error('Could not process files from folder');
    }
  }
  async PurchaseGoodsJSON_Direct(): Promise<any[]> {

    const nonHazardWaste: any[] = [];
    let record;
    // await this.sapResponseRepository.deleteAll({sapId: 'SAP7'})
    try {
      for (const parent of purchase) {
        for (const item of parent) {
          if (item.Total) {
            let newObj: any = {
              sapId: "SAP4",
              dataType: 4,
              Date: DateTime.fromFormat(item.Month, 'LLL-yyyy').toFormat('yyyyMMdd'),
              fetched_on: DateTime.utc().toString(),
              TotalSpent: item.Total,
              MaterialCategory: item['Material_Category'],
              userProfileId: 289,
              Title: 'Purchase Goods & Services',
              tier0_id: null,
              tier1_id: 103,
              tier2_id: null,
              tier3_id: null,
              level: 1,
              locationId: 103,
              Month: item.Month,
              Plant: 'India',
              UoM: 'USD',
              Location: 'India'
            }
            nonHazardWaste.push(newObj)
          }
        }
        await this.pushUpdatedOnly(nonHazardWaste, 5000, 'SAP4')
      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    }

    return nonHazardWaste;
  }

  private async fetchParquetFromS3(bucketName: string, key: string): Promise<Buffer> {
    const params = {Bucket: bucketName, Key: key};

    try {
      const data = await s3.getObject(params).promise();
      return data.Body as Buffer;
    } catch (error) {
      console.error('Error fetching parquet from S3:', error);
      throw new Error('Could not fetch the parquet file from S3');
    }
  }

  private async FuelJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const records: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          Title: "Fuel",
          sapId: "SAP1",
          dataType: 1,
          Location: record.Location,
          Date: record.Date,
          fetched_on: DateTime.utc().toString(),
          userProfileId: 289,// Add the userProfileId here

        };



        if (record.Qty) newObj.Quantity = record.Qty
        if (record.UOM) newObj.UoM = record.UOM; // Mapping UOM to DPA0132
        if (record.Fuel) newObj.FuelType = record.Fuel; // Mapping Fuel to DPA0131
        // if (record.Date) newObj.DPAN460 = record.Date; // Mapping Date to DPAN460


        if (record.Location && (record.Location.trim().toLowerCase().includes('hosur') || record.Location.trim().toLowerCase().includes('hsr'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;
          newObj.Plant = 'Hosur';
        }
        else if (record.Location && record.Location.trim().toLowerCase().includes('karantaka') || record.Location.trim().toLowerCase().includes('mysr') || record.Location.trim().toLowerCase().includes('mysore')) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;
          newObj.Plant = 'Mysore';
        } else if (record.Location && (record.Location.trim().toLowerCase().includes('solan') || record.Location.trim().toLowerCase().includes('hp'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 132;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 132;
          newObj.Plant = 'Nalagarh';
        }
        records.push(newObj);
        console.log('Transformed Record:', newObj);

      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return records;
  }
  private async HazardousJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const hazardWaste: any[] = [];
    let record;
    let count = 0
    try {
      while ((record = await cursor.next())) {
        // if (record.MaterialDescription) newObj.DPAN0042 = record.MaterialDescription; // Mapping UOM to DPA0132

        let newObj: any = {
          sapId: "SAP2",
          dataType: 2,
          Location: record.Location,
          Date: record.Date,
          WasteCategory: record.Category,
          UoM: record.UOM,
          userProfileId: 289,// Add the userProfileId here
          Title: 'Hazardous',
          fetched_on: DateTime.utc().toString()

        };


        // if (record.MaterialDescription) newObj.DPAN0042 = record.MaterialDescription; // Mapping UOM to DPA0132
        if (record.Qty) newObj.Quantity = record.Qty;


        if (record.MaterialDescription) {

          newObj.WasteDescription = record.MaterialDescription;
        } else if (record.WasteCategory) {

          newObj.WasteDescription = record.WasteCategory;
        } else if (record.Category) {

          newObj.WasteDescription = record.Category;
        }

        // console.log('WasteCategory:', record.WasteCategory);


        if (record.Location && (record.Location.trim().toLowerCase().includes('hosur') || record.Location.trim().toLowerCase().includes('hos'))) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;
          newObj.Plant = 'Hosur';

          hazardWaste.push(newObj);
          // console.log('Transformed Record:', newObj);

        }
        else if (record.Location && (record.Location.trim().toLowerCase().includes('karantaka') || record.Location.trim().toLowerCase().includes('mysr') || record.Location.trim().toLowerCase().includes('mysore'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;
          newObj.Plant = 'Mysore';

          hazardWaste.push(newObj);
          // console.log('Transformed Record:', newObj);
        } else if (record.Location && (record.Location.trim().toLowerCase().includes('hp') || record.Location.trim().toLowerCase().includes('solan'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 132;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 132;
          newObj.Plant = 'Nalagarh';
          hazardWaste.push(newObj);

        } else {
          console.log(record)
        }



      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return hazardWaste
  }
  private async NonHazardousJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          sapId: "SAP3",
          dataType: 3,
          Location: record.Location,
          Date: record.Date,
          fetched_on: DateTime.utc().toString(),
          UoM: record.UOM,
          userProfileId: 289,// Add the userProfileId here
          Title: 'Non-Hazardous'

        };


        if (record.MaterialDescription) newObj.WasteDescription = record.MaterialDescription; // Mapping UOM to DPA0132
        if (record.Qty) newObj.Quantity = record.Qty; // Mapping Qty to DPA0336

        if (record.Location && (record.Location.trim().toLowerCase().includes('hosur') || record.Location.trim().toLowerCase().includes('hos'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;
          newObj.Plant = 'Hosur';

          nonHazardWaste.push(newObj);


        }
        else if (record.Location && (record.Location.trim().toLowerCase().includes('karantaka') || record.Location.trim().toLowerCase().includes('mysr') || record.Location.trim().toLowerCase().includes('mysore'))) {


          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;
          newObj.Plant = 'Mysore';
          nonHazardWaste.push(newObj);

        }
        else if (record.Location && (record.Location.trim().toLowerCase().includes('hp') || record.Location.trim().toLowerCase().includes('solan'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 132;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 132;
          newObj.Plant = 'Nalagarh';
          nonHazardWaste.push(newObj);

        }
        else if (record.Location && record.Location.trim().toLowerCase().includes('karawang')) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 133;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 133;
          newObj.Plant = 'Karawang';
          nonHazardWaste.push(newObj);

        } else {
          console.log(record)
        }


      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async PurchaseGoodsJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          sapId: "SAP4",
          dataType: 4,
          UoM: record.UOM,
          userProfileId: 289,
          Title: 'Purchase Goods & Services',
          fetched_on: DateTime.utc().toString(),
          Location: record.PLANT,
          Date: record.DATE,

          PurchaseOrder: record.PURCHASE_ORDER,
          TotalSpent: record.NETPAY,
          HSNCode: record.HSN_CODE,
          VendorCode: record.VENDOR_CODE,
          SupplierName: record.SUPPLIER_NAME,


        };
        console.log(record)

        if (record.MATERIAL_DESCRIPTION) newObj.MaterialDescription = record.MATERIAL_DESCRIPTION; // Mapping UOM to DPA0132
        if (record.QUANTITY) newObj.Quantity = record.QUANTITY; // Mapping Qty to DPA0336

        if (record.PLANT && (record.PLANT.trim().toLowerCase().includes('hosur') || record.PLANT.trim().toLowerCase().includes('hos'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = 103;
          newObj.tier2_id = null;
          newObj.tier3_id = null;
          newObj.level = 1;
          newObj.locationId = 103;
          newObj.Plant = 'India';
          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);

        }
        else if (record.PLANT && (record.PLANT.trim().toLowerCase().includes('karantaka') || record.PLANT.trim().toLowerCase().includes('mysr') || record.PLANT.trim().toLowerCase().includes('mysore'))) {

          newObj.tier0_id = null;
          newObj.tier1_id = 103;
          newObj.tier2_id = null;
          newObj.tier3_id = null;
          newObj.level = 1;
          newObj.locationId = 103;
          newObj.Plant = 'India';
          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);
        }

        else if (record.PLANT && record.PLANT.trim().toLowerCase().includes('karawang')) {

          newObj.tier0_id = null;
          newObj.tier1_id = 103;
          newObj.tier2_id = null;
          newObj.tier3_id = null;
          newObj.level = 1;
          newObj.locationId = 103;
          newObj.Plant = 'India';
          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);
        } else if (record.PLANT && (record.PLANT.trim().toLowerCase().includes('hp') || record.PLANT.trim().toLowerCase().includes('solan'))) {

          newObj.tier0_id = null;
          newObj.tier1_id = 103;
          newObj.tier2_id = null;
          newObj.tier3_id = null;
          newObj.level = 1;
          newObj.locationId = 103;
          newObj.Plant = 'India';
          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);
        }


      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async CapitalGoodsJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          sapId: "SAP5",
          dataType: 5,
          Location: record.PLANT,
          Currency: record.Currency,
          Date: record.DATE,
          fetched_on: DateTime.utc().toString(),
          PurchaseOrder: record.PURCHASE_ORDER,
          TotalSpent: record.NETPAY,
          HSNCode: record.HSN_CODE,
          VendorCode: record.VENDOR_CODE,
          SupplierName: record.SUPPLIER_NAME,
          UoM: record.UOM,
          userProfileId: 289,// Add the userProfileId here
          Title: 'Captial Goods'

        };


        if (record.MATERIAL_DESCRIPTION) newObj.MaterialDescription = record.MATERIAL_DESCRIPTION; // Mapping UOM to DPA0132
        if (record.QUANTITY) newObj.Quantity = record.QUANTITY; // Mapping Qty to DPA0336

        if (record.PLANT && (record.PLANT.trim().toLowerCase().includes('hosur') || record.PLANT.trim().toLowerCase().includes('hos'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;
          newObj.Plant = 'Hosur';
          nonHazardWaste.push(newObj);


        }
        else if (record.PLANT && (record.PLANT.trim().toLowerCase().includes('karantaka') || record.PLANT.trim().toLowerCase().includes('mysr') || record.PLANT.trim().toLowerCase().includes('mysore'))) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;
          newObj.Plant = 'Mysore';
          nonHazardWaste.push(newObj);

        }

        else if (record.PLANT && record.PLANT.trim().toLowerCase().includes('karawang')) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 133;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 133;
          newObj.Plant = 'Karawang';
          nonHazardWaste.push(newObj);

        } else if (record.PLANT && (record.PLANT.trim().toLowerCase().includes('hp') || record.PLANT.trim().toLowerCase().includes('solan'))) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 132;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 132;
          newObj.Plant = 'Nalagarh';
          nonHazardWaste.push(newObj);

        } else {
          console.log('failed', record)
        }


      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async BusinessTravelJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          sapId: "SAP6",
          dataType: 6,
          Location: 'Corporate',

          fetched_on: DateTime.utc().toString(),
          BTOrigin: record.Origin,
          BTDestination: record.Destination,
          EmpId: record['EmployeeID'],
          BTMode: record['ModeOfTransportation'],
          userProfileId: 289,// Add the userProfileId here
          Title: 'Business Travel'

        };
        console.log(record)

        if (newObj.EmpId && newObj.BTOrigin && newObj.BTDestination && newObj.BTMode) {
          newObj.tier0_id = 0;
          newObj.tier1_id = null;
          newObj.tier2_id = null;
          newObj.tier3_id = null;
          newObj.level = 0;
          newObj.locationId = 0;
          newObj.Plant = 'Corporate';
          console.log(newObj)
          nonHazardWaste.push(newObj);
        }


      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async DownstreamJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          sapId: "SAP7",
          dataType: 7,
          Date: DateTime.fromFormat(record.Month + '' + record.Year, 'Myyyy').toFormat('yyyyMMdd'),
          fetched_on: DateTime.utc().toString(),
          DestinationLocation: record.DestinationLocation,
          OriginLocation: record.OriginLocation,
          MaterialDescription: record.MaterialDescription,
          Currency: record.Currency,
          InvoiceAmount: record.InvoiceAmount,
          Quantity: record.Qty,
          MaterialNumber: record['MaterialNumber'],
          ProductType: record['ProductType'],
          userProfileId: 289,
          Title: 'Downstream Transportation and Distribution'

        };


        if (newObj.OriginLocation && newObj.DestinationLocation && newObj.MaterialNumber && newObj.ProductType && newObj.MaterialDescription) {
          newObj.tier0_id = null;
          newObj.tier1_id = 103;
          newObj.tier2_id = null;
          newObj.tier3_id = null;
          newObj.level = 1;
          newObj.locationId = 103;

          newObj.Plant = '';
          newObj.Location = 'India';
          console.log(newObj)
          nonHazardWaste.push(newObj);
        }


      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async HREmployeeJSON(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          sapId: "SAP8",
          dataType: 8,
          fetched_on: DateTime.utc().toString(),
          OfficeLocation: record.office_location,
          OfficeCity: record.office_city,
          Gender: record.gender,
          DOB: record.date_of_birth,
          EmpType: record.employee_type,
          ContractType: record.contract_type,
          EmpId: record.employee_id,
          userProfileId: 289,
          Title: 'Employee Data'

        };
        let india = ["india", "hosur plant", "mysore plant", "ahmedabad", "nalagarh plant", "javadhu hills", "mysore", "allahabad", "electronic city", "thirukkurungudi", "padavedu", "jodhpur", "corp. office", "chennai", "anekal", "hosur", "bangalore", "chandigarh", "tirunelveli", "bhopal", "hubli", "thirupathi", "koramangala", "raipur", "mumbai", "hyderabad", "khammam", "noida", "coimbatore", "delhi", "trivandrum", "pune", "rajkot", "jaipur", "kolkata", "bhubaneshwar", "ranchi", "gulbarga", "nagpur", "pondicherry", "cochin", "vellore", "amritsar", "bhatinda", "gwalior", "surat", "patna", "tiruppur", "aligarh", "jalandhar", "guwahati", "mangalore", "varanasi", "saharanpur", "indore", "villupuram", "lucknow", "ajmer", "vijayawada", "vizag", "solapur", "ludhiana", "madurai", "sivagangai", "udaipur", "kurnool", "trichy", "ambala", "alwar", "navatirupathi", "aurangabad", "srivaikuntam", "alwarthirunagari", "guntur", "gorakhpur", "karnal", "calicut", "karaikudi", "jalgaon", "kota", "purnia", "kanpur", "faridabad", "hissar", "namakkal", "jabalpur", "erode", "tanjore", "anantapur", "agra", "salem", "bikaner", "karim nagar", "moradabad", "arani", "davanagere", "rajahmundry", "kumbakonam", "baroda", "dehradun", "warangal", "srirangam", "kolhapur", "ujjian", "bareilly", "sirkazhi", "harur", "uluberia", "sambalpur", "tiruvallur", "nasik", "meerut", "gaya", "siliguri", "nanded", "basti", "jhansi", "haldwani", "krishnagiri", "muzafarpur", "ghaziabad"]
        let brazil = ['sao paulo', 'brazil']
        let colombia = ['colombia']
        let dubai = ['dubai']
        let guatemala = ['guatemala city', 'guatemala']
        let indonesia = ['indonesia']
        let italy = ['rome', 'italy']
        let mexico = ['mexico']
        let panama = ['panama city', 'panama']
        let singapore = ['singapore']
        let philippines = ['philippines']

        if (Object.values(newObj).filter(i => i).length === 12) {


          if (india.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 103;
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.locationId = 103;
            newObj.Plant = record.office_city;
            newObj.Location = 'India';
            nonHazardWaste.push(newObj);
          } else if (indonesia.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 105;
            newObj.locationId = 105;
            newObj.Location = 'Indonesia';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (indonesia.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 105;
            newObj.locationId = 105;
            newObj.Location = 'Indonesia';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (singapore.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 107;
            newObj.locationId = 107;
            newObj.Location = 'Singapore';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (colombia.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 108;
            newObj.locationId = 108;
            newObj.Location = 'Colombia';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (italy.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 109;
            newObj.locationId = 109;
            newObj.Location = 'Italy';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (mexico.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 110;
            newObj.locationId = 110;
            newObj.Location = 'Mexico';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (guatemala.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 111;
            newObj.locationId = 111;
            newObj.Location = 'Guatemala';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (panama.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 112;
            newObj.locationId = 112;
            newObj.Location = 'Panama';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (dubai.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 113;
            newObj.locationId = 113;
            newObj.Location = 'Dubai';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (philippines.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 114;
            newObj.locationId = 114;
            newObj.Location = 'Philippines';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          } else if (brazil.includes(record.office_city.toString().toLowerCase())) {
            newObj.tier0_id = null;
            newObj.tier1_id = 115;
            newObj.locationId = 115;
            newObj.Location = 'Brazil';
            newObj.tier2_id = null;
            newObj.tier3_id = null;
            newObj.level = 1;
            newObj.Plant = record.office_city;
            nonHazardWaste.push(newObj);
          }

        }
      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async fuelToJson(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const records: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          Location: record.Location,
          Date: record.Date,
          fetched_on: DateTime.utc().toString(),
          userProfileId: 289,// Add the userProfileId here

        };



        if (record.Qty) newObj.DPA0336 = record.Qty; // Mapping Qty to DPA0336
        if (record.UOM) newObj.DPA0132 = record.UOM; // Mapping UOM to DPA0132
        if (record.Fuel) newObj.DPA0131 = record.Fuel; // Mapping Fuel to DPA0131
        // if (record.Date) newObj.DPAN460 = record.Date; // Mapping Date to DPAN460


        if (record.Location && (record.Location.trim().toLowerCase().includes('hosur') || record.Location.trim().toLowerCase().includes('hosur'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;

        }
        else if (record.Location && (record.Location.trim().toLowerCase().includes('mysore') || record.Location.trim().toLowerCase().includes('mysr') || record.Location.trim().toLowerCase().includes('karnataka'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;
        } else if (record.Location && record.Location.trim().toLowerCase().includes('solan')) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 132;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 132;
        }
        records.push(newObj);
        console.log('Transformed Record:', newObj);

      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return records;
  }
  private async hazardToJson(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const hazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          Location: record.Location,
          Date: record.Date,
          Category: record.Category,
          UOM: record.UOM,
          userProfileId: 289,// Add the userProfileId here
          Type: 'Hazardous',
          fetched_on: DateTime.utc().toString()

        };


        // if (record.MaterialDescription) newObj.DPAN0042 = record.MaterialDescription; // Mapping UOM to DPA0132
        if (record.Qty) newObj.DPAN0500 = record.Qty;

        if (record.MaterialDescription) {
          newObj.DPAN0041 = record.MaterialDescription;
        } else if (record.WasteCategory) {
          newObj.DPAN0041 = record.WasteCategory;
        } else if (record.Category) {
          newObj.DPAN0041 = record.Category;
        }
        // console.log('WasteCategory:', record.WasteCategory);


        if (record.Location && (record.Location.trim().toLowerCase().includes('hosur') || record.Location.trim().toLowerCase().includes('hos'))) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;

          hazardWaste.push(newObj);
          // console.log('Transformed Record:', newObj);

        }
        else if (record.Location && record.Location.trim().toLowerCase().includes('karantaka')) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;

          hazardWaste.push(newObj);
          // console.log('Transformed Record:', newObj);
        }



      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return hazardWaste;
  }
  private async nonHazardToJson(buffer: Buffer): Promise<any[]> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const nonHazardWaste: any[] = [];
    let record;

    try {
      while ((record = await cursor.next())) {
        //  console.log('Pushing record:', record);
        // records.push(record);
        let newObj: any = {
          Location: record.Location,
          Date: record.Date,
          fetched_on: DateTime.utc().toString(),
          UOM: record.UOM,
          userProfileId: 289,// Add the userProfileId here
          Type: 'Non-Hazardous'

        };


        if (record.MaterialDescription) newObj.DPAN0042 = record.MaterialDescription; // Mapping UOM to DPA0132
        if (record.Qty) newObj.DPAN0043 = record.Qty; // Mapping Qty to DPA0336

        if (record.Location && (record.Location.trim().toLowerCase().includes('hosur') || record.Location.trim().toLowerCase().includes('hos'))) {
          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 130;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 130;

          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);

        }
        else if (record.Location && record.Location.trim().toLowerCase().includes('karantaka')) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 131;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 131;

          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);
        }

        else if (record.Location && record.Location.trim().toLowerCase().includes('karawang')) {

          newObj.tier0_id = null;
          newObj.tier1_id = null;
          newObj.tier2_id = 133;
          newObj.tier3_id = null;
          newObj.level = 2;
          newObj.locationId = 133;

          nonHazardWaste.push(newObj);
          console.log('Transformed Record:', newObj);
        }


      }
    } catch (error) {
      console.error('Error processing Parquet data:', error);
    } finally {
      await reader.close();
    }

    return nonHazardWaste;
  }
  private async saveFuelBatch(records: any[], batchSize: number): Promise<void> {
    const batches = Math.ceil(records.length / batchSize);
    for (let i = 0; i < batches; i++) {
      const batch = records.slice(i * batchSize, (i + 1) * batchSize);
      const s3Records = batch.map(record => new SapFuel(record));
      // console.log(s3Records)
      await this.s3Repository.createAll(s3Records);
      console.log(`Batch ${i + 1} of ${batches} saved.`);
    }
  }
  private async pushData(records: any[], batchSize: number): Promise<void> {
    const batches = Math.ceil(records.length / batchSize);
    for (let i = 0; i < batches; i++) {
      const batch = records.slice(i * batchSize, (i + 1) * batchSize);

      const s3Records = batch.map(record => new SapResponse(record));
      // console.log(s3Records)
      await this.sapResponseRepository.createAll(s3Records);
      console.log(`Batch ${i + 1} of ${batches} saved.`);
    }
  }
  async pushUpdatedOnly(records: any[], batchSize: number, sapId: string): Promise<void> {
    const batches = Math.ceil(records.length / batchSize);
    for (let i = 0; i < batches; i++) {
      const batch = records.slice(i * batchSize, (i + 1) * batchSize);
      const s3Records = batch.map(record => new SapResponse(record));
      for (const record of s3Records) {
        if (record?.MaterialCategory) {
          const titleToSearch = record?.MaterialCategory?.trim()?.toLowerCase() || '';
          const efCategory = await this.newEfSubcategory1Repository.findOne({where: {title: {like: titleToSearch}}, include: [{relation: 'newEfSubcategory2s', scope: {where: {fields: {id: true}}}}]})
          if (efCategory && efCategory?.newEfSubcategory2s && efCategory.newEfSubcategory2s?.length) {
            const efKey = efCategory.id + '-' + efCategory?.newEfSubcategory2s[0].id
            const existing = await this.sapResponseRepository.findOne({
              where: {
                Month: record.Month as string, sapId, userProfileId: 289,
                MaterialCategory: record.MaterialCategory as string
              }
            });

            if (existing) {
              const TotalSpent = existing.TotalSpent + record.TotalSpent;
              await this.sapResponseRepository.updateById(existing.id, {TotalSpent, efKey});
            } else {

              await this.sapResponseRepository.create({...record, efKey});
            }
          }
        }
      }

      console.log(`Batch ${i + 1} of ${batches} saved.`, DateTime.utc().setZone('Asia/Kolkata').toFormat('yyyy-MM-dd HH:mm:ss'));
    }
  }
  private async pushUpdatedOnlyDownStream(records: any[], batchSize: number, sapId: string): Promise<void> {
    const batches = Math.ceil(records.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const batch = records.slice(i * batchSize, (i + 1) * batchSize);

      const s3Records = batch.map(record => new SapResponse(record));
      for (const record of s3Records) {

        if (record?.ModeOfTransportation) {
          const titleToSearch = record?.ModeOfTransportation?.trim()?.toLowerCase() || '';
          const efCategory = await this.newEfSubcategory1Repository.findOne({where: {title: {like: titleToSearch}, newEfCategoryId: 60}, include: [{relation: 'newEfSubcategory2s', scope: {where: {fields: {id: true}}}}]})

          if (efCategory && efCategory?.newEfSubcategory2s && efCategory.newEfSubcategory2s?.length) {
            const efKey = efCategory.id + '-' + efCategory?.newEfSubcategory2s[0].id
            const existing = await this.sapResponseRepository.findOne({
              where: {
                Month: record.Month as string, sapId, userProfileId: 289, ModeOfTransportation: record.ModeOfTransportation as string
              }
            });

            if (existing) {

              const InvoiceAmount = existing.InvoiceAmount + record.InvoiceAmount;
              await this.sapResponseRepository.updateById(existing.id, {InvoiceAmount, efKey});
            } else {

              await this.sapResponseRepository.create({...record, efKey});
            }
          }
        }
      }

      console.log(`Batch ${i + 1} of ${batches} saved.`, DateTime.utc().setZone('Asia/Kolkata').toFormat('yyyy-MM-dd HH:mm:ss'));
    }
  }
  private async saveHazardBatch(hazardWaste: any[], batchSize: number): Promise<void> {
    const batches = Math.ceil(hazardWaste.length / batchSize);
    for (let i = 0; i < batches; i++) {
      const batch = hazardWaste.slice(i * batchSize, (i + 1) * batchSize);
      const s3Records = batch.map(record => new SapHazardous(record));
      // console.log(s3Records)
      await this.s3HazardRepository.createAll(s3Records);
      console.log(`Batch ${i + 1} of ${batches} saved.`);
    }
  }





  private async saveNonHazardBatch(nonHazardWaste: any[], batchSize: number): Promise<void> {
    const batches = Math.ceil(nonHazardWaste.length / batchSize);
    for (let i = 0; i < batches; i++) {
      const batch = nonHazardWaste.slice(i * batchSize, (i + 1) * batchSize);
      const s3Records = batch.map(record => new SapNonHazardous(record));
      // console.log(s3Records)
      await this.s3NonHazardRepository.createAll(s3Records);
      console.log(`Batch ${i + 1} of ${batches} saved.`);
    }
  }

}
